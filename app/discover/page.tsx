'use client'

import { useState, useEffect, useCallback } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import Link from 'next/link'
import { Search, Users, BookOpen, Heart } from 'lucide-react'
import { FollowButton } from '@/components/FollowButton'
import Image from 'next/image'

interface Member {
  id: string
  name: string
  bio?: string
  avatar?: string
  profile_picture_url?: string
  follower_count: number
  subscriber_count: number
  entry_count: number
  is_following?: boolean
}

export default function DiscoverPage() {
  const [members, setMembers] = useState<Member[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [filter, setFilter] = useState<'all' | 'new' | 'popular'>('all')
  const [user, setUser] = useState<{ id: string; role: string } | null>(null)
  const supabase = createSupabaseClient()

  const checkUser = useCallback(async () => {
    try {
      const { data: { user: authUser } } = await supabase.auth.getUser()
      if (authUser) {
        const { data: profile } = await supabase
          .from('users')
          .select('*')
          .eq('id', authUser.id)
          .single()
        setUser(profile)
      }
    } catch (error) {
      console.error('Error checking user:', error)
    }
  }, [supabase])

  const loadMembers = useCallback(async () => {
    setLoading(true)
    try {
      // Get ALL platform members (not just content creators)
      const { data: users, error } = await supabase
        .from('users')
        .select('id, name, bio, avatar, profile_picture_url, follower_count, subscriber_count, entry_count, created_at')
        .limit(200) // Get more users for better discovery

      if (error) {
        console.error('Error loading users:', error)
        setLoading(false)
        return
      }

      // Use all users as the base data
      const data = users || []



      // Filter out current user and prepare members list
      let membersWithFollowStatus = (data || [])
        .filter(member => user ? member.id !== user.id : true) // Exclude current user

      // Check which members the current user is following (if logged in)
      if (user) {
        const { data: follows } = await supabase
          .from('follows')
          .select('writer_id')
          .eq('follower_id', user.id)

        const followingIds = new Set(follows?.map(f => f.writer_id) || [])
        membersWithFollowStatus = membersWithFollowStatus.map(member => ({
          ...member,
          is_following: followingIds.has(member.id)
        }))
      }

      // Apply sorting based on filter
      if (filter === 'popular') {
        // Sort by combined engagement: subscribers (weighted higher) + followers
        membersWithFollowStatus.sort((a, b) => {
          const aScore = ((a.subscriber_count || 0) * 2) + (a.follower_count || 0)
          const bScore = ((b.subscriber_count || 0) * 2) + (b.follower_count || 0)
          return bScore - aScore
        })
      } else if (filter === 'new') {
        // Sort by newest members first
        membersWithFollowStatus.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
      } else {
        // Most Engaging: Sort by total activity (entry count + engagement)
        membersWithFollowStatus.sort((a, b) => {
          const aEngagement = (a.entry_count || 0) + (a.follower_count || 0) + (a.subscriber_count || 0)
          const bEngagement = (b.entry_count || 0) + (b.follower_count || 0) + (b.subscriber_count || 0)
          return bEngagement - aEngagement
        })
      }

      setMembers(membersWithFollowStatus)
    } catch (err) {
      console.error('Error loading members:', err)
    } finally {
      setLoading(false)
    }
  }, [filter, user, supabase])

  useEffect(() => {
    checkUser()
  }, [checkUser])

  useEffect(() => {
    if (user !== null) { // Load even if user is null (not logged in)
      loadMembers()
    }
  }, [user, loadMembers])

  // const toggleBookmark = async (creatorId: string) => {
  //   if (!user) {
  //     console.log('No user found for subscription action')
  //     return
  //   }

  //   console.log('Toggling subscription for creator:', creatorId, 'User:', user.id)
  //   setBookmarkingId(creatorId)

  //   try {
  //     const creator = creators.find(c => c.id === creatorId)
  //     if (!creator) {
  //       console.log('Creator not found:', creatorId)
  //       return
  //     }

  //     if (creator.is_bookmarked) {
  //       // Remove subscription
  //       console.log('Removing subscription...')
  //       const { data, error } = await supabase
  //         .from('subscriptions')
  //         .delete()
  //         .eq('subscriber_id', user.id)
  //         .eq('writer_id', creatorId)

  //       console.log('Remove subscription result:', { data, error })

  //       if (!error) {
  //         setCreators(prev => prev.map(c =>
  //           c.id === creatorId
  //             ? { ...c, is_bookmarked: false, bookmark_count: Math.max(0, c.bookmark_count - 1) }
  //             : c
  //         ))
  //         console.log('Subscription removed successfully')
  //       } else {
  //         console.error('Error removing subscription:', error)
  //       }
  //     } else {
  //       // Add subscription (redirect to subscription page)
  //       console.log('Redirecting to subscription page...')
  //       window.location.href = `/u/${creatorId}`
  //     }
  //   } catch (error) {
  //     console.error('Error toggling subscription:', error)
  //     alert('Something went wrong. Please try again.')
  //   } finally {
  //     setBookmarkingId(null)
  //   }
  // }

  const filteredMembers = members.filter(member =>
    member.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    member.bio?.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
        
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-serif text-gray-800 mb-2">
            Discover People
          </h1>
          <p className="text-gray-600 font-serif">
            Find and follow members of the OnlyDiary community
          </p>
        </div>

        {/* Search and Filters */}
        <div className="mb-8 space-y-4">
          <div className="relative max-w-2xl">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search people by name or bio..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-12 pr-4 py-4 bg-white rounded-2xl border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-800 placeholder:text-gray-600 md:placeholder:text-gray-500"
            />
          </div>

          <div className="flex gap-2">
            <button
              onClick={() => setFilter('all')}
              className={`px-4 py-2 rounded-xl font-medium transition-colors ${
                filter === 'all'
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-600 hover:bg-gray-50'
              }`}
            >
              Most Engaging
            </button>
            <button
              onClick={() => setFilter('popular')}
              className={`px-4 py-2 rounded-xl font-medium transition-colors ${
                filter === 'popular'
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-600 hover:bg-gray-50'
              }`}
            >
              Most Popular
            </button>
            <button
              onClick={() => setFilter('new')}
              className={`px-4 py-2 rounded-xl font-medium transition-colors ${
                filter === 'new'
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-600 hover:bg-gray-50'
              }`}
            >
              Newest
            </button>
          </div>
        </div>

        {/* Enhanced Loading State */}
        {loading ? (
          <div className="text-center py-12">
            {/* Enhanced Loading Spinner */}
            <div className="relative inline-flex items-center justify-center mb-6">
              <div className="animate-spin rounded-full h-16 w-16 border-4 border-gray-200"></div>
              <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-600 border-t-transparent absolute"></div>
              <div className="absolute text-2xl">👥</div>
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Discovering People...</h2>
            <p className="text-gray-600 mb-12">Finding members of the OnlyDiary community</p>

            {/* Loading skeleton for members */}
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
              {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((i) => (
                <div key={i} className="bg-white rounded-xl p-4 shadow-sm animate-pulse">
                  <div className="text-center">
                    <div className="w-14 h-14 bg-gray-200 rounded-full mx-auto mb-3"></div>
                    <div className="h-3 bg-gray-200 rounded w-3/4 mx-auto mb-1"></div>
                    <div className="h-2 bg-gray-200 rounded w-full mb-3"></div>
                    <div className="flex justify-center gap-2 mb-3">
                      <div className="h-2 bg-gray-200 rounded w-8"></div>
                      <div className="h-2 bg-gray-200 rounded w-8"></div>
                    </div>
                    <div className="h-6 bg-gray-200 rounded w-16 mx-auto"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : filteredMembers.length > 0 ? (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
            {filteredMembers.map((member) => (
              <div
                key={member.id}
                className="bg-white rounded-xl p-4 shadow-sm hover:shadow-md transition-all duration-300 relative group"
              >
                <Link href={`/u/${member.id}`} className="block">
                  <div className="text-center">
                    <div className="w-14 h-14 rounded-full bg-gray-200 flex items-center justify-center mx-auto mb-3 overflow-hidden">
                      {(() => {
                        // Prioritize profile_picture_url over avatar for better reliability
                        const avatarSrc = member.profile_picture_url || member.avatar
                        // Check if avatar is a valid URL (not an emoji)
                        const isValidUrl = avatarSrc && (avatarSrc.startsWith('http') || avatarSrc.startsWith('/'))

                        if (isValidUrl) {
                          return (
                            <img
                              src={avatarSrc as string}
                              alt={member.name || 'Member avatar'}
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                // Hide the broken image and show fallback
                                const target = e.target as HTMLImageElement
                                target.style.display = 'none'
                                const parent = target.parentElement
                                if (parent) {
                                  parent.innerHTML = `<span class="text-lg font-serif text-gray-500">${member.name.charAt(0).toUpperCase()}</span>`
                                }
                              }}
                            />
                          )
                        } else if (avatarSrc && !isValidUrl) {
                          // If avatar is an emoji, display it directly
                          return (
                            <span className="text-xl">{avatarSrc}</span>
                          )
                        } else {
                          // Default fallback
                          return (
                            <span className="text-lg font-serif text-gray-500">
                              {member.name.charAt(0).toUpperCase()}
                            </span>
                          )
                        }
                      })()}
                    </div>

                    <h3 className="text-sm font-serif text-gray-800 mb-1 line-clamp-1">
                      {member.name}
                    </h3>

                    {member.bio && (
                      <p className="text-gray-600 text-xs mb-3 line-clamp-1">
                        {member.bio}
                      </p>
                    )}

                    <div className="flex justify-center gap-3 text-xs text-gray-500 mb-3">
                      <div className="flex items-center gap-1">
                        <Users className="w-3 h-3" />
                        <span>{member.follower_count || 0}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Heart className="w-3 h-3" />
                        <span>{member.subscriber_count || 0}</span>
                      </div>
                    </div>
                  </div>
                </Link>

                {/* Follow Action */}
                {user && (
                  <div className="mt-3">
                    <FollowButton
                      writerId={member.id}
                      writerName={member.name}
                      initialIsFollowing={member.is_following}
                    />
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Search className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-xl font-serif text-gray-800 mb-2">No people found</h3>
            <p className="text-gray-600">
              {searchQuery
                ? 'Try adjusting your search terms'
                : user
                ? 'You&apos;re already following all available members! Check back later for new people.'
                : 'No people match the current filter'
              }
            </p>
            {user && !searchQuery && (
              <div className="mt-4">
                <Link
                  href="/timeline"
                  className="inline-flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors"
                >
                  📖 Go to Timeline
                </Link>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
